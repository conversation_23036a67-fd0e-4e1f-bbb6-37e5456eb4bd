#!/usr/bin/env node

const BASE_URL = 'http://localhost:5000';

async function checkData() {
  try {
    const fetch = (await import('node-fetch')).default;
    
    // Get messages
    const response = await fetch(`${BASE_URL}/api/messages?userId=1&startDate=2024-01-15&endDate=2024-01-16`);
    const messages = await response.json();
    
    console.log(`Total messages: ${messages.length}`);
    console.log('\nMessages by IMEI:');
    
    // Group by IMEI
    const byImei = {};
    messages.forEach(msg => {
      if (!byImei[msg.imei]) byImei[msg.imei] = [];
      byImei[msg.imei].push(msg);
    });
    
    Object.keys(byImei).forEach(imei => {
      console.log(`\nIMEI ${imei}: ${byImei[imei].length} messages`);
      
      // Sort by timestamp
      const sorted = byImei[imei].sort((a, b) => 
        new Date(a.satelliteTimestamp) - new Date(b.satelliteTimestamp)
      );
      
      sorted.forEach((msg, i) => {
        const time = new Date(msg.satelliteTimestamp).toLocaleString();
        console.log(`  ${i+1}. ${time} - ${msg.status} (${msg.latitude}, ${msg.longitude})`);
      });
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkData();
