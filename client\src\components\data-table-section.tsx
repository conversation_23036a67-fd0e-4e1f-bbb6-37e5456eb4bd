import { useMemo, useEffect, useRef, useCallback, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { SatelliteMessage, Trip } from "@shared/schema";
import { getTripColor } from "@/lib/utils";
import { Download, ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { exportMessagesToKml, downloadFile } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface DataTableSectionProps {
  messages: SatelliteMessage[];
  selectedMessageId: number | null;
  dateRange: { start: Date | null; end: Date | null };
  onDateRangeChange: (range: { start: Date | null; end: Date | null }) => void;
  onRowClick: (messageId: number, lat: number, lng: number) => void;
  isLoading: boolean;
  trips: Trip[];
  selectedTripId: string | null;
  onTripChange: (tripId: string | null) => void;
}

export default function DataTableSection({
  messages,
  selectedMessageId,
  dateRange,
  onDateRangeChange,
  onRowClick,
  isLoading,
  trips,
  selectedTripId,
  onTripChange,
}: DataTableSectionProps) {
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  
  // Stato per l'ordinamento della tabella (per data satellite)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // Funzione per gestire il toggle dell'ordinamento
  const handleSortToggle = () => {
    setSortOrder(prevOrder => prevOrder === 'desc' ? 'asc' : 'desc');
  };
  
  // Filtra i viaggi in base al range di date e deduplica
  const filteredTrips = useMemo(() => {
    let tripsToFilter = trips;
    
    // Ottimizzazione: usa Map per deduplica O(n) invece di reduce O(n²)
    const uniqueTripsMap = new Map<string, Trip>();
    
    for (const trip of tripsToFilter) {
      const existing = uniqueTripsMap.get(trip.id);
      if (!existing) {
        uniqueTripsMap.set(trip.id, trip);
      } else {
        // Se esiste già, mantieni quello con endTime più recente (o quello senza endTime)
        if (!trip.endTime || (existing.endTime && new Date(trip.endTime) > new Date(existing.endTime))) {
          uniqueTripsMap.set(trip.id, trip);
        }
      }
    }
    
    const uniqueTrips = Array.from(uniqueTripsMap.values());
    
    if (!dateRange.start && !dateRange.end) {
      return uniqueTrips; // Se non ci sono filtri di data, mostra tutti i viaggi
    }

    return uniqueTrips.filter(trip => {
      const tripStart = new Date(trip.startTime);
      const tripEnd = trip.endTime ? new Date(trip.endTime) : new Date(); // Se non c'è endTime, usa ora
      
      // Verifica se il viaggio si sovrappone con il range di date
      if (dateRange.start && dateRange.end) {
        // Range completo: il viaggio deve sovrapporsi al range
        return tripStart <= dateRange.end && tripEnd >= dateRange.start;
      } else if (dateRange.start) {
        // Solo data di inizio: il viaggio deve iniziare dopo la data di inizio
        return tripStart >= dateRange.start;
      } else if (dateRange.end) {
        // Solo data di fine: il viaggio deve finire prima della data di fine
        return tripEnd <= dateRange.end;
      }
      
      return true;
    });
  }, [trips, dateRange]);

  // RIMOSSO: filtro ridondante dei messaggi - già filtrati nel hook useSatelliteData
  // I messaggi arrivano già filtrati per IMEI e date range dal hook
  const filteredMessages = useMemo(() => {
    // Ordina per timestamp satellitare basandosi sulla scelta dell'utente
    return [...messages].sort((a, b) => {
      const aTime = new Date(a.satelliteTimestamp).getTime();
      const bTime = new Date(b.satelliteTimestamp).getTime();
      
      if (sortOrder === 'desc') {
        return bTime - aTime; // Più recente prima (decrescente)
      } else {
        return aTime - bTime; // Più vecchio prima (crescente)
      }
    });
  }, [messages, sortOrder]);

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateValue = e.target.value;
    let date: Date | null = null;
    
    if (dateValue) {
      // Il valore dateValue è nel formato "YYYY-MM-DDTHH:MM"
      // Creiamo la data e forziamo che sia interpretata come ora locale
      // poi la convertiamo in UTC correttamente
      const localDate = new Date(dateValue);
      
      if (!isNaN(localDate.getTime())) {
        // La data è già in UTC nel senso che new Date() interpreta il valore
        // come se fosse già in UTC se non ha fuso orario esplicito.
        // Noi vogliamo invece che rappresenti l'ora locale, quindi:
        date = localDate;
      } else {
        date = null;
      }
    }
    
    // Validazione: la data di inizio non può essere successiva alla data di fine
    if (date && dateRange.end && date > dateRange.end) {
      toast({
        title: "Errore di validazione",
        description: "La data 'da' non può essere successiva alla data 'A'",
        variant: "destructive",
      });
      return; // Non aggiorna il dateRange se la validazione fallisce
    }
    
    // Forza sempre un nuovo oggetto anche se il valore è uguale
    onDateRangeChange({ start: date, end: dateRange.end ? new Date(dateRange.end) : null });
    console.log('[DataTableSection] Cambio data inizio:', dateValue, '->', date?.toISOString());
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateValue = e.target.value;
    let date: Date | null = null;
    
    if (dateValue) {
      // Stessa logica della data di inizio
      const localDate = new Date(dateValue);
      
      if (!isNaN(localDate.getTime())) {
        date = localDate;
      } else {
        date = null;
      }
    }
    
    // Validazione: la data di fine non può essere precedente alla data di inizio
    if (date && dateRange.start && date < dateRange.start) {
      toast({
        title: "Errore di validazione",
        description: "La data 'A' non può essere precedente alla data 'da'",
        variant: "destructive",
      });
      return; // Non aggiorna il dateRange se la validazione fallisce
    }
    
    onDateRangeChange({ start: dateRange.start ? new Date(dateRange.start) : null, end: date });
    console.log('[DataTableSection] Cambio data fine:', dateValue, '->', date?.toISOString());
  };

  // Se il viaggio selezionato non è più nel range filtrato, deselezionalo
  const handleDateRangeChange = (range: { start: Date | null; end: Date | null }) => {
    onDateRangeChange(range);
    
    // Verifica se il viaggio selezionato è ancora valido
    if (selectedTripId) {
      const isStillValid = filteredTrips.some(trip => trip.id === selectedTripId);
      if (!isStillValid) {
        onTripChange(null);
      }
    }
  };

  const getStatusBadge = useCallback((status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs";
    
    switch (status) {
      case 'Fixed':
        return (
          <span className={`${baseClasses} bg-blue-100 text-blue-800`}>
            <div className="w-2 h-2 bg-blue-600 rounded-full mr-1"></div>
            GPS fixed
          </span>
        );
      case 'No Fixed':
        return (
          <span className={`${baseClasses} bg-orange-100 text-orange-800`}>
            <div className="w-2 h-2 bg-orange-500 rounded-full mr-1"></div>
            GPS not fixed
          </span>
        );
      case 'Start':
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800`}>
            <div className="w-2 h-2 bg-green-600 rounded-full mr-1"></div>
            Inizio
          </span>
        );
      case 'End':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            <div className="w-2 h-2 bg-red-600 rounded-full mr-1"></div>
            Fine
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800`}>
            <div className="w-2 h-2 bg-gray-600 rounded-full mr-1"></div>
            {status}
          </span>
        );
    }
  }, []);

  const getBatteryBar = useCallback((percentage: number) => {
    const width = Math.max(percentage, 5); // Minimum 5% width for visibility
    const color = percentage > 50 ? 'bg-green-500' : percentage > 25 ? 'bg-yellow-500' : 'bg-red-500';
    
    return (
      <div className="flex items-center">
        <div className="w-12 h-2 bg-gray-200 rounded-full mr-2">
          <div className={`h-2 ${color} rounded-full`} style={{ width: `${width}%` }}></div>
        </div>
        <span className="text-xs text-gray-600">{percentage}%</span>
      </div>
    );
  }, []);

  const getDirectionText = useCallback((degrees: number): string => {
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SO', 'O', 'NO'];
    const index = Math.round(degrees / 45) % 8;
    return `${directions[index]} (${degrees}°)`;
  }, []);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('it-IT'); // Formato italiano
  };

  const formatDateTimeLocal = (date: Date) => {
    // Mostra la data così com'è, dato che ora non facciamo conversioni complicate
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  const formatDateTime = useCallback((date: Date) => {
    // Ottimizzazione: usa metodi più veloci invece di toLocaleString
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  }, []);

  // Formato semplificato per il selettore dei viaggi
  const formatTripDisplay = useCallback((trip: Trip) => {
    const startTime = new Date(trip.startTime);
    const month = String(startTime.getMonth() + 1).padStart(2, '0');
    const day = String(startTime.getDate()).padStart(2, '0');
    const hours = String(startTime.getHours()).padStart(2, '0');
    const minutes = String(startTime.getMinutes()).padStart(2, '0');
    return `${day}/${month} ${hours}:${minutes}`;
  }, []);

  // Effetto per scroll automatico alla riga selezionata
  useEffect(() => {
    if (selectedMessageId && tableContainerRef.current) {
      // Trova la riga selezionata
      const selectedRow = tableContainerRef.current.querySelector(`[data-message-id="${selectedMessageId}"]`);
      
      if (selectedRow) {
        // Calcola la posizione della riga rispetto al container
        const containerRect = tableContainerRef.current.getBoundingClientRect();
        const rowRect = selectedRow.getBoundingClientRect();
        
        // Calcola se la riga è visibile
        const isRowVisible = 
          rowRect.top >= containerRect.top && 
          rowRect.bottom <= containerRect.bottom;
        
        // Se la riga non è visibile, fai scroll
        if (!isRowVisible) {
          selectedRow.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }
    }
  }, [selectedMessageId]);

  // Funzione per gestire export e download KML
  const handleExportKml = () => {
    // Mostra il toast e salva il dismiss per usarlo nell'azione
    const t = toast({
      title: "Esporta in KML",
      description: "Vuoi scaricare tutti i messaggi visualizzati in tabella come file .kml?",
      persistent: true,
      action: (
        <div className="flex space-x-2">
          <button
            className="px-3 py-1 rounded bg-green-600 text-white hover:bg-green-700 text-sm"
            onClick={() => {
              // Converte satelliteTimestamp in stringa ISO
              const kml = exportMessagesToKml(filteredMessages.map(m => ({
                ...m,
                satelliteTimestamp: typeof m.satelliteTimestamp === 'string' ? m.satelliteTimestamp : m.satelliteTimestamp.toISOString(),
              })));
              downloadFile(
                `satellite-messaggi-${new Date().toISOString().slice(0, 19).replace(/[:T]/g, '-')}.kml`,
                kml,
                'application/vnd.google-earth.kml+xml'
              );
              t.dismiss();
              
              // Mostra toast di conferma con il numero di messaggi esportati
              toast({
                title: "Esportazione completata",
                description: `${filteredMessages.length} messaggi esportati in formato KML`,
              });
            }}
          >
            Scarica
          </button>
          <button
            className="px-3 py-1 rounded bg-gray-500 text-white hover:bg-gray-600 text-sm"
            onClick={() => t.dismiss()}
          >
            Annulla
          </button>
        </div>
      ),
    });
  };

  return (
    <div className="h-full flex flex-col bg-white overflow-hidden">
      {/* Table header with filters */}
      <div className="bg-gradient-to-r from-[#005c53] to-[#26a69a] border-b border-gray-200 px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-white">Dati Posizione</h3>
          <div className="flex items-center space-x-4">
            {/* Pulsante export KML */}
            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
              onClick={handleExportKml}
              title="Esporta messaggi in KML"
            >
              <Download className="w-4 h-4 mr-1" />
              KML
            </Button>
            <div className="flex items-center space-x-2">
              <Label className="text-sm text-gray-100">Viaggio:</Label>
              <Select
                value={selectedTripId || "all"}
                onValueChange={(value) => onTripChange(value === "all" ? null : value)}
              >
                <SelectTrigger className="w-48 text-sm">
                  <SelectValue placeholder="Seleziona viaggio" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tutti i viaggi ({filteredTrips.length})</SelectItem>
                  {filteredTrips.map((trip) => {
                    const tripColor = getTripColor(trip.id);
                    return (
                      <SelectItem key={trip.id} value={trip.id}>
                        <div className="flex items-center">
                          <div 
                            className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                            style={{ backgroundColor: tripColor }}
                          ></div>
                          <span>{formatTripDisplay(trip)}</span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Label className="text-sm text-gray-100">Da:</Label>
              <Input
                type="datetime-local"
                value={dateRange.start ? formatDateTimeLocal(dateRange.start) : ''}
                onChange={handleStartDateChange}
                className="border border-gray-300 rounded px-3 py-1 text-sm focus:ring-2 focus:ring-primary w-48"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Label className="text-sm text-gray-100">A:</Label>
              <Input
                type="datetime-local"
                value={dateRange.end ? formatDateTimeLocal(dateRange.end) : ''}
                onChange={handleEndDateChange}
                className="border border-gray-300 rounded px-3 py-1 text-sm focus:ring-2 focus:ring-primary w-48"
              />
            </div>
            <div className="flex items-center">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {filteredMessages.length} messaggi
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Table content */}
      <div ref={tableContainerRef} className="flex-1 overflow-auto custom-scrollbar min-h-0">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-lg text-gray-600">Caricamento messaggi...</div>
          </div>
        ) : (
          <table className="w-full">
            <thead className="bg-gray-100 sticky top-0">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <button 
                    className="flex items-center space-x-1 hover:text-gray-900 transition-colors"
                    onClick={handleSortToggle}
                    title={`Ordina per data satellitare (${sortOrder === 'desc' ? 'decrescente' : 'crescente'})`}
                  >
                    <span>Orario Satellite</span>
                    {sortOrder === 'desc' ? (
                      <ArrowDown className="w-4 h-4" />
                    ) : (
                      <ArrowUp className="w-4 h-4" />
                    )}
                  </button>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Orario Server
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Coordinate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Velocità
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Direzione
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Batteria
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Stato
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMessages.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-2 text-center text-gray-500">
                    Nessun messaggio trovato per i criteri selezionati
                  </td>
                </tr>
              ) : (
                filteredMessages.map((message) => (
                  <tr
                    key={message.id}
                    data-message-id={message.id}
                    onClick={() => onRowClick(message.id, message.latitude, message.longitude)}
                    className={`hover:bg-gray-50 cursor-pointer ${
                      selectedMessageId === message.id ? 'bg-blue-50' : ''
                    }`}
                  >
                    <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                      {formatDateTime(new Date(message.satelliteTimestamp))}
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-600">
                      {formatDateTime(new Date(message.serverTimestamp))}
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                      {message.latitude.toFixed(4)}, {message.longitude.toFixed(4)}
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                      {message.speed} km/h
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                      {getDirectionText(message.direction)}
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap">
                      {getBatteryBar(message.batteryPercentage)}
                    </td>
                    <td className="px-6 py-2 whitespace-nowrap">
                      {getStatusBadge(message.status)}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}

