#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

console.log('🧪 Starting SatelliteTracker Test Suite');
console.log('=====================================');

const BASE_URL = 'http://localhost:5000';

// Test data
const testData = {
  admin: { username: 'admin', password: 'admin123' },
  testFile: path.join(__dirname, 'satellite-messages-test.json')
};

// Helper function to make HTTP requests
async function makeRequest(url, options = {}) {
  const fetch = (await import('node-fetch')).default;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response;
}

// Test 1: Login as admin
async function testLogin() {
  console.log('\n📝 Test 1: Admin Login');
  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      body: JSON.stringify(testData.admin)
    });
    
    const result = await response.json();
    console.log('✅ Login successful:', result.user.username);
    return result.user;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    throw error;
  }
}

// Test 2: Import test messages
async function testImportMessages() {
  console.log('\n📤 Test 2: Import Test Messages');
  try {
    const FormData = (await import('form-data')).default;
    const form = new FormData();
    
    const fileBuffer = fs.readFileSync(testData.testFile);
    form.append('file', fileBuffer, {
      filename: 'satellite-messages-test.json',
      contentType: 'application/json'
    });
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(`${BASE_URL}/api/admin/messages/import`, {
      method: 'POST',
      body: form
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('✅ Import successful:', result.message);
    console.log(`   📊 Imported ${result.count} messages`);
    return result;
  } catch (error) {
    console.error('❌ Import failed:', error.message);
    throw error;
  }
}

// Test 3: Fetch messages and verify trips
async function testFetchMessages(user) {
  console.log('\n📥 Test 3: Fetch Messages and Verify Trips');
  try {
    const response = await makeRequest(
      `${BASE_URL}/api/messages?userId=${user.id}&startDate=2024-01-15&endDate=2024-01-16`
    );
    
    const messages = await response.json();
    console.log(`✅ Fetched ${messages.length} messages`);
    
    // Group messages by IMEI for analysis
    const messagesByImei = {};
    messages.forEach(msg => {
      if (!messagesByImei[msg.imei]) {
        messagesByImei[msg.imei] = [];
      }
      messagesByImei[msg.imei].push(msg);
    });
    
    console.log('\n📊 Message Analysis:');
    Object.keys(messagesByImei).forEach(imei => {
      const imeiMessages = messagesByImei[imei];
      console.log(`   IMEI ${imei}: ${imeiMessages.length} messages`);
      
      // Sort by timestamp
      imeiMessages.sort((a, b) => new Date(a.satelliteTimestamp) - new Date(b.satelliteTimestamp));
      
      // Analyze message types
      const statusCounts = {};
      imeiMessages.forEach(msg => {
        statusCounts[msg.status] = (statusCounts[msg.status] || 0) + 1;
      });
      
      console.log(`     Status distribution:`, statusCounts);
    });
    
    return messages;
  } catch (error) {
    console.error('❌ Fetch messages failed:', error.message);
    throw error;
  }
}

// Test 4: Simulate client-side trip creation
async function testTripCreation(messages) {
  console.log('\n🚗 Test 4: Client-Side Trip Creation Simulation');
  
  // Simple trip creation logic (similar to client-side)
  const GAP_THRESHOLD_MS = 10 * 60 * 1000; // 10 minutes
  
  // Group by IMEI
  const messagesByImei = {};
  messages.forEach(msg => {
    if (!messagesByImei[msg.imei]) {
      messagesByImei[msg.imei] = [];
    }
    messagesByImei[msg.imei].push(msg);
  });
  
  let totalTrips = 0;
  
  Object.keys(messagesByImei).forEach(imei => {
    const imeiMessages = messagesByImei[imei];
    
    // Sort by timestamp
    imeiMessages.sort((a, b) => new Date(a.satelliteTimestamp) - new Date(b.satelliteTimestamp));
    
    let trips = [];
    let currentTripMessages = [];
    let lastMessageTime = null;
    
    imeiMessages.forEach(message => {
      const messageTime = new Date(message.satelliteTimestamp);
      
      let shouldStartNewTrip = false;
      
      if (!lastMessageTime) {
        shouldStartNewTrip = true;
      } else {
        const gapMs = messageTime.getTime() - lastMessageTime.getTime();
        if (gapMs > GAP_THRESHOLD_MS) {
          shouldStartNewTrip = true;
        }
      }
      
      if (shouldStartNewTrip && currentTripMessages.length > 0) {
        trips.push({
          id: `trip_${imei}_${new Date(currentTripMessages[0].satelliteTimestamp).getTime()}`,
          imei: imei,
          startTime: currentTripMessages[0].satelliteTimestamp,
          endTime: currentTripMessages[currentTripMessages.length - 1].satelliteTimestamp,
          messageCount: currentTripMessages.length
        });
        currentTripMessages = [];
      }
      
      currentTripMessages.push(message);
      lastMessageTime = messageTime;
    });
    
    // Don't forget the last trip
    if (currentTripMessages.length > 0) {
      trips.push({
        id: `trip_${imei}_${new Date(currentTripMessages[0].satelliteTimestamp).getTime()}`,
        imei: imei,
        startTime: currentTripMessages[0].satelliteTimestamp,
        endTime: currentTripMessages[currentTripMessages.length - 1].satelliteTimestamp,
        messageCount: currentTripMessages.length
      });
    }
    
    console.log(`   IMEI ${imei}: ${trips.length} trips created`);
    trips.forEach((trip, index) => {
      const start = new Date(trip.startTime).toLocaleString();
      const end = new Date(trip.endTime).toLocaleString();
      console.log(`     Trip ${index + 1}: ${start} → ${end} (${trip.messageCount} messages)`);
    });
    
    totalTrips += trips.length;
  });
  
  console.log(`✅ Total trips created: ${totalTrips}`);
  return totalTrips;
}

// Main test runner
async function runTests() {
  try {
    console.log('🔍 Checking if server is running...');
    await makeRequest(`${BASE_URL}/api/health`).catch(() => {
      throw new Error('Server is not running. Please start with: npm run dev');
    });
    console.log('✅ Server is running');
    
    const user = await testLogin();
    await testImportMessages();
    const messages = await testFetchMessages(user);
    const tripCount = await testTripCreation(messages);
    
    console.log('\n🎉 Test Suite Completed Successfully!');
    console.log('=====================================');
    console.log(`📊 Summary:`);
    console.log(`   - Messages imported and fetched: ${messages.length}`);
    console.log(`   - Trips created: ${tripCount}`);
    console.log(`   - Expected trips: 6 main trips + isolated messages`);
    
    if (tripCount >= 6) {
      console.log('✅ Trip creation test PASSED');
    } else {
      console.log('⚠️  Trip creation test: fewer trips than expected');
    }
    
  } catch (error) {
    console.error('\n❌ Test Suite Failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
runTests();
