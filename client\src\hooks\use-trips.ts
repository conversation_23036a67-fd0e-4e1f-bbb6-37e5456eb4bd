import { useMemo } from "react";
import type { SatelliteMessage, Trip } from "@shared/schema";
import { createTripsFromMessages, getTripMessages, filterTripsByDateRange } from "@/lib/trip-utils";

/**
 * Hook for client-side trip management
 * Creates trips from satellite messages based on temporal gaps
 */
export function useTrips(
  messages: SatelliteMessage[],
  selectedImei?: string,
  startDate?: Date | null,
  endDate?: Date | null
) {
  // Create trips from all messages
  const allTrips = useMemo(() => {
    if (!messages || !Array.isArray(messages)) return [];
    return createTripsFromMessages(messages);
  }, [messages]);

  // Filter trips by selected IMEI
  const imeiFilteredTrips = useMemo(() => {
    if (!selectedImei) return allTrips;
    return allTrips.filter(trip => trip.imei === selectedImei);
  }, [allTrips, selectedImei]);

  // Filter trips by date range
  const filteredTrips = useMemo(() => {
    return filterTripsByDateRange(imeiFilteredTrips, startDate, endDate);
  }, [imeiFilteredTrips, startDate, endDate]);

  // Function to get messages for a specific trip
  const getTripMessagesById = (tripId: string): SatelliteMessage[] => {
    if (!messages || !Array.isArray(messages)) return [];
    return getTripMessages(tripId, messages);
  };

  return {
    trips: filteredTrips,
    allTrips,
    getTripMessages: getTripMessagesById,
    isLoading: false // Client-side trips are never loading
  };
}

/**
 * Hook for getting messages for a specific trip
 * This replaces the server-side /api/trips/:tripId/messages endpoint
 */
export function useTripMessages(tripId: string | null, allMessages: SatelliteMessage[]) {
  const messages = useMemo(() => {
    if (!tripId || !allMessages || !Array.isArray(allMessages)) return [];
    return getTripMessages(tripId, allMessages);
  }, [tripId, allMessages]);

  return {
    messages,
    isLoading: false // Client-side trip messages are never loading
  };
}
