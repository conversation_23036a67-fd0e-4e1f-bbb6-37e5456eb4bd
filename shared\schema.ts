import { z } from "zod";

// User type definitions
export type User = {
  id: number;
  username: string;
  password: string;
  imeis: string[];
  role: string;
};

export type InsertUser = {
  username: string;
  password: string;
  imeis: string[];
  role?: string;
};

// Satellite message type definitions
export type SatelliteMessage = {
  id: number;
  imei: string;
  satelliteTimestamp: Date;
  serverTimestamp: Date;
  latitude: number;
  longitude: number;
  speed: number;
  direction: number;
  batteryPercentage: number;
  status: string;
};

export type InsertSatelliteMessage = {
  imei: string;
  satelliteTimestamp: Date;
  latitude: number;
  longitude: number;
  speed: number;
  direction: number;
  batteryPercentage: number;
  status: string;
};

// Validation schemas
export const insertUserSchema = z.object({
  username: z.string().min(1),
  password: z.string().min(1),
  imeis: z.array(z.string()),
  role: z.string().optional().default('user'),
});

export const insertSatelliteMessageSchema = z.object({
  imei: z.string(),
  satelliteTimestamp: z.date(),
  latitude: z.number(),
  longitude: z.number(),
  speed: z.number(),
  direction: z.number(),
  batteryPercentage: z.number(),
  status: z.string(),
});

export const loginSchema = z.object({
  username: z.string().min(1),
  password: z.string().min(1),
});

export type LoginRequest = z.infer<typeof loginSchema>;

// Trip types (client-side only)
export type Trip = {
  id: string;
  imei: string;
  startTime: Date;
  endTime: Date | null;
  startMessage: SatelliteMessage;
  endMessage?: SatelliteMessage;
};



// WebSocket message types
export type WebSocketMessage = {
  type: 'new_message';
  data: SatelliteMessage;
} | {
  type: 'device_status';
  data: {
    imei: string;
    online: boolean;
  };
};
