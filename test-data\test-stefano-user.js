#!/usr/bin/env node

console.log('🧪 Testing with <PERSON> Use<PERSON> (IMEI: 7028893029)');
console.log('===============================================');

const BASE_URL = 'http://localhost:5000';

async function makeRequest(url, options = {}) {
  const fetch = (await import('node-fetch')).default;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response;
}

async function testStefanoUser() {
  try {
    // Login as Stefano
    console.log('\n📝 Login as Stefano');
    const loginResponse = await makeRequest(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      body: JSON.stringify({ username: 'ste<PERSON><PERSON>', password: 'stefano' })
    });
    
    const loginResult = await loginResponse.json();
    console.log('✅ Login successful:', loginResult.user.username);
    console.log('   IMEIs:', loginResult.user.imeis);
    
    // Fetch messages for the test dates
    console.log('\n📥 Fetching messages for 2024-01-15 to 2024-01-16');
    const messagesResponse = await makeRequest(
      `${BASE_URL}/api/messages?userId=${loginResult.user.id}&startDate=2024-01-15&endDate=2024-01-16`
    );
    
    const messages = await messagesResponse.json();
    console.log(`✅ Found ${messages.length} messages for IMEI 7028893029`);
    
    if (messages.length > 0) {
      console.log('\n📊 Message Details:');
      
      // Sort by timestamp
      const sortedMessages = messages.sort((a, b) => 
        new Date(a.satelliteTimestamp) - new Date(b.satelliteTimestamp)
      );
      
      // Show first 10 messages
      sortedMessages.slice(0, 10).forEach((msg, i) => {
        const time = new Date(msg.satelliteTimestamp).toLocaleString();
        console.log(`  ${i+1}. ${time} - ${msg.status} (${msg.latitude}, ${msg.longitude})`);
      });
      
      if (messages.length > 10) {
        console.log(`  ... and ${messages.length - 10} more messages`);
      }
      
      // Test trip creation
      console.log('\n🚗 Creating trips from messages...');
      const trips = createTripsFromMessages(sortedMessages);
      
      console.log(`✅ Created ${trips.length} trips:`);
      trips.forEach((trip, i) => {
        const start = new Date(trip.startTime).toLocaleString();
        const end = trip.endTime ? new Date(trip.endTime).toLocaleString() : 'N/A';
        const duration = trip.endTime ? 
          ((new Date(trip.endTime) - new Date(trip.startTime)) / (1000 * 60)).toFixed(1) : '0';
        
        console.log(`  ${i+1}. ${start} → ${end} (${trip.messageCount} messages, ${duration} min)`);
      });
      
      const mainTrips = trips.filter(t => t.messageCount >= 5);
      console.log(`\n📈 Summary: ${mainTrips.length} main trips, ${trips.length - mainTrips.length} short trips`);
      
    } else {
      console.log('⚠️  No messages found. Make sure to import test data first.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

function createTripsFromMessages(messages) {
  if (!messages || !Array.isArray(messages) || messages.length === 0) return [];

  const GAP_THRESHOLD_MS = 10 * 60 * 1000; // 10 minutes
  const trips = [];
  let currentTripMessages = [];
  let lastMessageTime = null;

  for (const message of messages) {
    const messageTime = new Date(message.satelliteTimestamp);
    
    let shouldStartNewTrip = false;
    
    if (!lastMessageTime) {
      shouldStartNewTrip = true;
    } else {
      const gapMs = messageTime.getTime() - lastMessageTime.getTime();
      if (gapMs > GAP_THRESHOLD_MS) {
        shouldStartNewTrip = true;
      }
    }
    
    if (shouldStartNewTrip && currentTripMessages.length > 0) {
      const trip = {
        id: `trip_${currentTripMessages[0].imei}_${new Date(currentTripMessages[0].satelliteTimestamp).getTime()}`,
        imei: currentTripMessages[0].imei,
        startTime: currentTripMessages[0].satelliteTimestamp,
        endTime: currentTripMessages[currentTripMessages.length - 1].satelliteTimestamp,
        messageCount: currentTripMessages.length
      };
      trips.push(trip);
      currentTripMessages = [];
    }
    
    currentTripMessages.push(message);
    lastMessageTime = messageTime;
  }
  
  // Don't forget the last trip
  if (currentTripMessages.length > 0) {
    const trip = {
      id: `trip_${currentTripMessages[0].imei}_${new Date(currentTripMessages[0].satelliteTimestamp).getTime()}`,
      imei: currentTripMessages[0].imei,
      startTime: currentTripMessages[0].satelliteTimestamp,
      endTime: currentTripMessages[currentTripMessages.length - 1].satelliteTimestamp,
      messageCount: currentTripMessages.length
    };
    trips.push(trip);
  }
  
  return trips;
}

testStefanoUser();
