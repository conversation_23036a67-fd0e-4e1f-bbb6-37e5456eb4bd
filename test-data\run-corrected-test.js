#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

console.log('🧪 Starting CORRECTED SatelliteTracker Test Suite');
console.log('================================================');

const BASE_URL = 'http://localhost:5000';

// Test data
const testData = {
  admin: { username: 'admin', password: 'admin123' },
  testFile: path.join(__dirname, 'satellite-messages-test.json')
};

// Helper function to make HTTP requests
async function makeRequest(url, options = {}) {
  const fetch = (await import('node-fetch')).default;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response;
}

// Test 1: Login as admin
async function testLogin() {
  console.log('\n📝 Test 1: Admin Login');
  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      body: JSON.stringify(testData.admin)
    });
    
    const result = await response.json();
    console.log('✅ Login successful:', result.user.username);
    return result.user;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    throw error;
  }
}

// Test 2: Clear existing data (if any)
async function clearExistingData() {
  console.log('\n🗑️  Test 2: Clear Existing Data');
  try {
    // This is a simple approach - we'll just note that we're starting fresh
    console.log('✅ Ready to import fresh test data');
  } catch (error) {
    console.error('❌ Clear data failed:', error.message);
    // Don't throw - this is not critical
  }
}

// Test 3: Import corrected test messages
async function testImportMessages() {
  console.log('\n📤 Test 3: Import Corrected Test Messages');
  try {
    const FormData = (await import('form-data')).default;
    const form = new FormData();
    
    const fileBuffer = fs.readFileSync(testData.testFile);
    form.append('file', fileBuffer, {
      filename: 'satellite-messages-test.json',
      contentType: 'application/json'
    });
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(`${BASE_URL}/api/admin/messages/import`, {
      method: 'POST',
      body: form
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('✅ Import successful:', result.message);
    console.log(`   📊 Imported ${result.count || 'unknown'} messages`);
    return result;
  } catch (error) {
    console.error('❌ Import failed:', error.message);
    throw error;
  }
}

// Test 4: Fetch messages and verify trips
async function testFetchMessages(user) {
  console.log('\n📥 Test 4: Fetch Messages and Verify Trips');
  try {
    const response = await makeRequest(
      `${BASE_URL}/api/messages?userId=${user.id}&startDate=2024-01-15&endDate=2024-01-16`
    );
    
    const messages = await response.json();
    console.log(`✅ Fetched ${messages.length} messages`);
    
    // Group messages by IMEI for analysis
    const messagesByImei = {};
    messages.forEach(msg => {
      if (!messagesByImei[msg.imei]) {
        messagesByImei[msg.imei] = [];
      }
      messagesByImei[msg.imei].push(msg);
    });
    
    console.log('\n📊 Message Analysis:');
    Object.keys(messagesByImei).forEach(imei => {
      const imeiMessages = messagesByImei[imei];
      console.log(`   IMEI ${imei}: ${imeiMessages.length} messages`);
      
      // Sort by timestamp
      imeiMessages.sort((a, b) => new Date(a.satelliteTimestamp) - new Date(b.satelliteTimestamp));
      
      // Show first few messages with timestamps
      console.log('     First few messages:');
      imeiMessages.slice(0, 5).forEach((msg, i) => {
        const time = new Date(msg.satelliteTimestamp).toLocaleTimeString();
        console.log(`       ${i+1}. ${time} - ${msg.status}`);
      });
      
      // Analyze message types
      const statusCounts = {};
      imeiMessages.forEach(msg => {
        statusCounts[msg.status] = (statusCounts[msg.status] || 0) + 1;
      });
      
      console.log(`     Status distribution:`, statusCounts);
    });
    
    return messages;
  } catch (error) {
    console.error('❌ Fetch messages failed:', error.message);
    throw error;
  }
}

// Test 5: Simulate client-side trip creation with corrected logic
async function testTripCreation(messages) {
  console.log('\n🚗 Test 5: Client-Side Trip Creation with Corrected Data');
  
  // Trip creation logic (similar to client-side)
  const GAP_THRESHOLD_MS = 10 * 60 * 1000; // 10 minutes
  
  // Group by IMEI
  const messagesByImei = {};
  messages.forEach(msg => {
    if (!messagesByImei[msg.imei]) {
      messagesByImei[msg.imei] = [];
    }
    messagesByImei[msg.imei].push(msg);
  });
  
  let totalTrips = 0;
  const allTrips = [];
  
  Object.keys(messagesByImei).forEach(imei => {
    const imeiMessages = messagesByImei[imei];
    
    // Sort by timestamp
    imeiMessages.sort((a, b) => new Date(a.satelliteTimestamp) - new Date(b.satelliteTimestamp));
    
    let trips = [];
    let currentTripMessages = [];
    let lastMessageTime = null;
    
    imeiMessages.forEach(message => {
      const messageTime = new Date(message.satelliteTimestamp);
      
      let shouldStartNewTrip = false;
      
      if (!lastMessageTime) {
        shouldStartNewTrip = true;
      } else {
        const gapMs = messageTime.getTime() - lastMessageTime.getTime();
        if (gapMs > GAP_THRESHOLD_MS) {
          shouldStartNewTrip = true;
        }
      }
      
      if (shouldStartNewTrip && currentTripMessages.length > 0) {
        const trip = {
          id: `trip_${imei}_${new Date(currentTripMessages[0].satelliteTimestamp).getTime()}`,
          imei: imei,
          startTime: currentTripMessages[0].satelliteTimestamp,
          endTime: currentTripMessages[currentTripMessages.length - 1].satelliteTimestamp,
          messageCount: currentTripMessages.length
        };
        trips.push(trip);
        allTrips.push(trip);
        currentTripMessages = [];
      }
      
      currentTripMessages.push(message);
      lastMessageTime = messageTime;
    });
    
    // Don't forget the last trip
    if (currentTripMessages.length > 0) {
      const trip = {
        id: `trip_${imei}_${new Date(currentTripMessages[0].satelliteTimestamp).getTime()}`,
        imei: imei,
        startTime: currentTripMessages[0].satelliteTimestamp,
        endTime: currentTripMessages[currentTripMessages.length - 1].satelliteTimestamp,
        messageCount: currentTripMessages.length
      };
      trips.push(trip);
      allTrips.push(trip);
    }
    
    console.log(`   IMEI ${imei}: ${trips.length} trips created`);
    trips.forEach((trip, index) => {
      const start = new Date(trip.startTime).toLocaleString();
      const end = new Date(trip.endTime).toLocaleString();
      const duration = (new Date(trip.endTime) - new Date(trip.startTime)) / (1000 * 60); // minutes
      console.log(`     Trip ${index + 1}: ${start} → ${end} (${trip.messageCount} messages, ${duration.toFixed(1)} min)`);
    });
    
    totalTrips += trips.length;
  });
  
  console.log(`✅ Total trips created: ${totalTrips}`);
  
  // Analyze trip quality
  const mainTrips = allTrips.filter(trip => trip.messageCount >= 5);
  const shortTrips = allTrips.filter(trip => trip.messageCount < 5);
  
  console.log(`   📊 Trip Analysis:`);
  console.log(`     - Main trips (≥5 messages): ${mainTrips.length}`);
  console.log(`     - Short trips (<5 messages): ${shortTrips.length}`);
  
  return { totalTrips, mainTrips: mainTrips.length, shortTrips: shortTrips.length };
}

// Main test runner
async function runTests() {
  try {
    console.log('🔍 Checking if server is running...');
    await makeRequest(`${BASE_URL}/api/health`).catch(() => {
      throw new Error('Server is not running. Please start with: npm run dev');
    });
    console.log('✅ Server is running');
    
    const user = await testLogin();
    await clearExistingData();
    await testImportMessages();
    const messages = await testFetchMessages(user);
    const tripStats = await testTripCreation(messages);
    
    console.log('\n🎉 CORRECTED Test Suite Completed!');
    console.log('===================================');
    console.log(`📊 Final Results:`);
    console.log(`   - Messages imported and fetched: ${messages.length}`);
    console.log(`   - Total trips created: ${tripStats.totalTrips}`);
    console.log(`   - Main trips (≥5 messages): ${tripStats.mainTrips}`);
    console.log(`   - Short trips (<5 messages): ${tripStats.shortTrips}`);
    console.log(`   - Expected main trips: 6`);
    
    if (tripStats.mainTrips >= 6) {
      console.log('✅ Trip creation test PASSED - Correct number of main trips!');
    } else {
      console.log('⚠️  Trip creation test: fewer main trips than expected');
    }
    
    if (tripStats.totalTrips >= 6 && tripStats.totalTrips <= 12) {
      console.log('✅ Overall trip logic test PASSED!');
    } else {
      console.log('⚠️  Overall trip count outside expected range');
    }
    
  } catch (error) {
    console.error('\n❌ Test Suite Failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
runTests();
