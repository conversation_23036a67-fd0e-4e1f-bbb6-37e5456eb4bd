import type { SatelliteMessage, Trip } from "@shared/schema";

/**
 * Creates trips from satellite messages based on temporal gaps
 * Groups consecutive messages with gaps ≤ 10 minutes between them
 * When gap exceeds 10 minutes, ends current trip and starts a new one
 */
export function createTripsFromMessages(messages: SatelliteMessage[]): Trip[] {
  if (!messages || !Array.isArray(messages) || messages.length === 0) return [];

  // Group messages by IMEI first
  const messagesByImei = new Map<string, SatelliteMessage[]>();
  
  for (const message of messages) {
    if (!messagesByImei.has(message.imei)) {
      messagesByImei.set(message.imei, []);
    }
    messagesByImei.get(message.imei)!.push(message);
  }

  const trips: Trip[] = [];

  // Process each IMEI separately
  for (const [imei, imeiMessages] of Array.from(messagesByImei.entries())) {
    // Sort messages by satellite timestamp (ascending)
    const sortedMessages = imeiMessages.sort((a: SatelliteMessage, b: SatelliteMessage) =>
      new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime()
    );

    const imeiTrips = createTripsForImei(imei, sortedMessages);
    trips.push(...imeiTrips);
  }

  // Sort trips by start time (descending - most recent first)
  return trips.sort((a, b) => 
    new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
  );
}

/**
 * Creates trips for a single IMEI based on temporal gaps
 */
function createTripsForImei(imei: string, sortedMessages: SatelliteMessage[]): Trip[] {
  if (sortedMessages.length === 0) return [];

  const trips: Trip[] = [];
  const GAP_THRESHOLD_MS = 10 * 60 * 1000; // 10 minutes in milliseconds

  let currentTripMessages: SatelliteMessage[] = [];
  let lastMessageTime: Date | null = null;

  for (const message of sortedMessages) {
    const messageTime = new Date(message.satelliteTimestamp);

    // Determine if we should start a new trip
    let shouldStartNewTrip = false;

    if (!lastMessageTime) {
      // First message
      shouldStartNewTrip = true;
    } else {
      // Check gap with previous message
      const gapMs = messageTime.getTime() - lastMessageTime.getTime();
      if (gapMs > GAP_THRESHOLD_MS) {
        shouldStartNewTrip = true;
      }
    }

    if (shouldStartNewTrip && currentTripMessages.length > 0) {
      // Finalize current trip and start a new one
      const trip = createTripFromMessages(currentTripMessages);
      if (trip) {
        trips.push(trip);
      }
      currentTripMessages = [];
    }

    currentTripMessages.push(message);
    lastMessageTime = messageTime;
  }

  // Don't forget the last trip
  if (currentTripMessages.length > 0) {
    const trip = createTripFromMessages(currentTripMessages);
    if (trip) {
      trips.push(trip);
    }
  }

  return trips;
}

/**
 * Creates a single trip from a group of messages
 */
function createTripFromMessages(messages: SatelliteMessage[]): Trip | null {
  if (messages.length === 0) return null;

  // Sort messages by timestamp to ensure correct start/end
  const sortedMessages = messages.sort((a, b) => 
    new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime()
  );

  const startMessage = sortedMessages[0];
  const endMessage = sortedMessages.length > 1 ? sortedMessages[sortedMessages.length - 1] : null;

  // Generate trip ID based on IMEI and start timestamp
  const startTimestamp = new Date(startMessage.satelliteTimestamp).getTime();
  const tripId = `trip_${startMessage.imei}_${startTimestamp}`;

  return {
    id: tripId,
    imei: startMessage.imei,
    startTime: startMessage.satelliteTimestamp,
    endTime: endMessage ? endMessage.satelliteTimestamp : null,
    startMessage: startMessage,
    endMessage: endMessage || undefined
  };
}

/**
 * Gets messages for a specific trip ID
 * Since trips are created client-side, we need to recreate the trip logic
 * to find which messages belong to a specific trip
 */
export function getTripMessages(tripId: string, allMessages: SatelliteMessage[]): SatelliteMessage[] {
  if (!tripId || !allMessages || !Array.isArray(allMessages)) return [];

  // Extract IMEI and start timestamp from trip ID
  const tripIdParts = tripId.split('_');
  if (tripIdParts.length < 3) return [];

  const imei = tripIdParts[1];
  const startTimestamp = parseInt(tripIdParts[2]);

  if (isNaN(startTimestamp)) return [];

  // Filter messages for this IMEI
  const imeiMessages = allMessages
    .filter(msg => msg.imei === imei)
    .sort((a, b) => new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime());

  // Recreate trips to find the specific trip
  const trips = createTripsForImei(imei, imeiMessages);
  const targetTrip = trips.find(trip => trip.id === tripId);
  
  if (!targetTrip) return [];

  // Return messages that fall within this trip's time range
  const startTime = new Date(targetTrip.startTime).getTime();
  const endTime = targetTrip.endTime ? new Date(targetTrip.endTime).getTime() : startTime;

  return imeiMessages.filter(msg => {
    const msgTime = new Date(msg.satelliteTimestamp).getTime();
    return msgTime >= startTime && msgTime <= endTime;
  });
}

/**
 * Filters trips by date range
 */
export function filterTripsByDateRange(
  trips: Trip[], 
  startDate?: Date | null, 
  endDate?: Date | null
): Trip[] {
  return trips.filter(trip => {
    const tripStart = new Date(trip.startTime);
    const tripEnd = trip.endTime ? new Date(trip.endTime) : new Date();
    
    // Check if trip overlaps with date range
    if (startDate && endDate) {
      // Full range: trip must overlap with range
      return tripStart <= endDate && tripEnd >= startDate;
    } else if (startDate) {
      // Only start date: trip must start after start date
      return tripStart >= startDate;
    } else if (endDate) {
      // Only end date: trip must end before end date
      return tripEnd <= endDate;
    }
    
    return true;
  });
}
